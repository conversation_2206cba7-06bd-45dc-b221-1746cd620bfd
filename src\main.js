import './assets/styles/tailwind.css';
import './assets/styles/main.css';     // Custom global styles
import { tanksData } from './data/tanks.js';
import { debounce } from './utils/helpers.js';
import { initPerformanceOptimizations, performanceMonitor, getCachedElement, getDebounced } from './utils/performance.js';
import { RUSSIAN_TO_INTERNAL_TYPE_MAP, ENGLISH_TO_INTERNAL_TYPE_MAP, getFilteredTanks as getFilteredTanksModule } from './services/FilterService.js';
import { initRouter } from './router/index.js';
import { TankSearchIndex } from './services/SearchService.js';
import { state } from './store/state.js';
import { setupSidebarMenuItems, hideAllSections, showSection, renderTankList, showTankDetails, onMenuSelected } from './utils/ui.js';
import { initTheme } from './utils/theme.js';

// Define the global render function immediately.
// This ensures it exists before any other script tries to call it.
window.applyFiltersAndRenderTankList = () => {
  const tankListContainer = document.getElementById('tank-list');
  if (!tankListContainer) {
    return;
  }

  // Get the current state of filters from the DOM.
  const nationFilterContainer = document.getElementById('nation-filter');
  const typeFilterContainer = document.getElementById('type-filter');
  state.selectedCountry = Array.from(nationFilterContainer.querySelectorAll('.filter-item.active')).map(btn => btn.dataset.country);
  state.selectedCategory = Array.from(typeFilterContainer.querySelectorAll('.filter-item.active')).map(btn => btn.dataset.category);

  // Get the filtered list of tanks and re-render the list.
  const filteredTanks = getFilteredTanksModule(state);
  renderTankList(filteredTanks);
};

document.addEventListener('DOMContentLoaded', () => {
  console.log('Основной JS-файл загружен - минимальная версия DOMContentLoaded');

  // This single line feeds the entire application.
  state.allTanks = tanksData;

  // Initialize application components
  try {
    initTheme();
    initPerformanceOptimizations();
    setupSidebarMenuItems(); // No longer needs dependencies passed in
    initRouter(state);
    console.log('Application components initialized.');
  } catch (error) {
    console.error('Error during application initialization:', error);
  }

  // The click handler now simply toggles the class and calls the main rendering function.
  const handleFilterClick = (event) => {
    const target = event.target.closest('.filter-item');
    if (!target) return;

    const filterGroup = target.parentElement;
    if (target.classList.contains('active')) {
      target.classList.remove('active');
    } else {
      // Allow multiple selections for nations, but only one for type
      if (filterGroup.id === 'type-filter') {
        filterGroup.querySelectorAll('.filter-item.active').forEach(item => item.classList.remove('active'));
      }
      target.classList.add('active');
    }

    window.applyFiltersAndRenderTankList();
  };

  // Attach listeners
  const nationFilterContainer = document.getElementById('nation-filter');
  const typeFilterContainer = document.getElementById('type-filter');
  const tankListContainer = document.getElementById('tank-list');

  if (nationFilterContainer && typeFilterContainer) {
    nationFilterContainer.addEventListener('click', handleFilterClick);
    typeFilterContainer.addEventListener('click', handleFilterClick);
  }

  // Delegated event listener for tank clicks
  if (tankListContainer) {
    tankListContainer.addEventListener('click', (event) => {
      const tankItem = event.target.closest('.tank-item');
      if (tankItem && tankItem.dataset.tankId) {
        const tankId = tankItem.dataset.tankId;
        showTankDetails(tankId, tanksData);
      }
    });
  }

  console.log('Tank filtering and rendering initialized.');

  // Make the entire app visible now that everything is initialized.
  document.getElementById('app').style.visibility = 'visible';

  // Set the initial state directly by calling the refactored onMenuSelected function.
  onMenuSelected('vehicles', true);
});

// All other JavaScript code has been removed for testing.
// This comment is just to ensure the file ends cleanly.