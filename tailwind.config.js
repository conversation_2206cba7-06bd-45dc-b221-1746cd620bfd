/** @type {import('tailwindcss').Config} */
export default {
  content: ['./*.html', './src/**/*.{js,ts,jsx,tsx,vue,html}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      colors: {
        // Кастомные цвета для танков
        'tank-primary': '#7254e4',
        'tank-secondary': '#a17bf8',
        'tank-accent': '#ff6b6b',
        'glass-bg': 'rgba(255, 255, 255, 0.08)',
        'glass-border': 'rgba(255, 255, 255, 0.15)',
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        shimmer: 'shimmer 2s ease-in-out infinite',
        glow: 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(114, 84, 228, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(114, 84, 228, 0.8)' },
        },
      },
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        'tank-theme': {
          primary: '#7254e4',
          'primary-focus': '#5a3fb8',
          'primary-content': '#ffffff',
          secondary: '#a17bf8',
          'secondary-focus': '#8b5cf6',
          'secondary-content': '#ffffff',
          accent: '#ff6b6b',
          'accent-focus': '#ff5252',
          'accent-content': '#ffffff',
          neutral: '#1d1d2e',
          'neutral-focus': '#27233d',
          'neutral-content': '#e6e6e6',
          'base-100': '#1a1a2e',
          'base-200': '#16213e',
          'base-300': '#0f172a',
          'base-content': '#ffffff',
          info: '#3abff8',
          success: '#36d399',
          warning: '#fbbd23',
          error: '#f87272',
        },
      },
      'dark', // Fallback theme
    ],
    darkTheme: 'tank-theme',
    base: true,
    styled: true,
    utils: true,
    rtl: false,
    prefix: '',
    logs: true,
  },
};
