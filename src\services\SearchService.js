// Trie-based search index for fast prefix lookup of tank names
// Extracted from main.js for modular reuse

const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes
const searchCache = new Map();

class TrieNode {
  constructor() {
    this.children = new Map();
    this.tanks = new Set();
  }
}

export class TankSearchIndex {
  /**
   * @param {Array<Object>} tanks  Array of tank objects with at least { name } field.
   */
  constructor(tanks = []) {
    this.root = new TrieNode();
    if (Array.isArray(tanks) && tanks.length) {
      this.buildIndex(tanks);
    }
  }

  /**
   * Build index from list of tanks.
   * Supports indexing full name, each word, and truncated prefixes for fuzzy search.
   */
  buildIndex(tanks) {
    tanks.forEach(tank => {
      const lowerName = tank.name.toLowerCase();
      const wordsToIndex = new Set();

      // Split by space and index each part
      lowerName.split(' ').forEach(part => {
        if (part) wordsToIndex.add(part);
        if (part.length > 1) {
          wordsToIndex.add(part.substring(0, Math.max(1, part.length - 2)));
        }
      });

      // Whole name & first letter
      wordsToIndex.add(lowerName);
      if (lowerName.length > 0) wordsToIndex.add(lowerName[0]);

      // Insert each word / prefix into trie
      wordsToIndex.forEach(word => {
        if (word.length >= 1) {
          this.insert(word, tank);
        }
      });
    });
  }

  insert(word, tank) {
    let node = this.root;
    for (const char of word) {
      if (!node.children.has(char)) {
        node.children.set(char, new TrieNode());
      }
      node = node.children.get(char);
      node.tanks.add(tank);
    }
  }

  /**
   * Prefix search (case-insensitive).
   * Returns Set of matching tank objects.
   */
  search(prefix) {
    const lowerPrefix = prefix.toLowerCase();
    if (!lowerPrefix) return new Set();

    // Simple cache to avoid rebuilding result sets repeatedly
    const cached = searchCache.get(lowerPrefix);
    if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY) {
      return cached.result;
    }

    let node = this.root;
    for (const char of lowerPrefix) {
      if (!node.children.has(char)) return new Set();
      node = node.children.get(char);
    }

    const result = this.collectTanks(node);

    searchCache.set(lowerPrefix, { result, timestamp: Date.now() });
    if (searchCache.size > 500) searchCache.clear();

    return result;
  }

  collectTanks(node) {
    return new Set(node.tanks);
  }
}
