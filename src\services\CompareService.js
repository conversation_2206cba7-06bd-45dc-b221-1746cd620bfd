// Сервис для сравнения танков
import { createTankId } from '../utils/helpers.js';
import { getTankTypeClass, getFlagPath } from '../utils/constants.js';

export class CompareService {
  constructor() {
    this.tanks = [null, null];
  }

  addTank(tank, slotIndex) {
    if (slotIndex < 0 || slotIndex > 1) return false;

    const tankWithId = tank.id ? tank : { ...tank, id: createTankId(tank.name) };
    this.tanks[slotIndex] = tankWithId;
    this.updateSlotDisplay(slotIndex);
    this.updateCompareButton();
    return true;
  }

  removeTank(slotIndex) {
    if (slotIndex < 0 || slotIndex > 1) return false;

    this.tanks[slotIndex] = null;
    this.updateSlotDisplay(slotIndex);
    this.updateCompareButton();
    return true;
  }

  clearAll() {
    this.tanks = [null, null];
    this.updateSlotDisplay(0);
    this.updateSlotDisplay(1);
    this.updateCompareButton();
    this.hideResults();
  }

  updateSlotDisplay(slotIndex) {
    const slot = document.querySelector(`[data-slot="${slotIndex + 1}"]`);
    if (!slot) return;

    const content = slot.querySelector('.tank-slot-content');
    const tank = this.tanks[slotIndex];

    if (tank) {
      const typeClass = getTankTypeClass(tank.type);
      const flagPath = getFlagPath(tank.country);

      content.innerHTML = `
        <div class="modern-tank-card">
          <div class="tank-card-background"></div>
          <div class="tank-card-content">
            <div class="tank-card-top">
              <div class="tank-icon-area">
                <img src="src/assets/images/tanks/${tank.id}.webp" alt="${tank.name}" class="tank-card-icon" onerror="this.style.display='none'">
              </div>
              <div class="tank-flag-area">
                <img src="${flagPath}" alt="${tank.country}" class="tank-card-flag">
              </div>
            </div>
            <div class="tank-card-bottom">
              <div class="tank-card-name">${tank.name}</div>
              <div class="tank-card-badge ${typeClass}">${tank.type}</div>
            </div>
          </div>
          <button class="tank-card-remove" onclick="window.compareSystem.removeTank(${slotIndex})" title="Удалить танк">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      `;
      slot.classList.add('has-tank');
    } else {
      content.innerHTML = `
        <div class="tank-placeholder">
          <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          <span class="placeholder-text">Выберите танк</span>
        </div>
      `;
      slot.classList.remove('has-tank');
    }
  }

  updateCompareButton() {
    const compareBtn = document.getElementById('compare-btn');
    if (!compareBtn) return;

    const hasBothTanks = this.tanks[0] && this.tanks[1];
    compareBtn.disabled = !hasBothTanks;

    if (hasBothTanks) {
      compareBtn.textContent = 'Сравнить характеристики';
    } else {
      compareBtn.textContent = 'Выберите два танка для сравнения';
    }
  }

  showResults() {
    const resultsArea = document.getElementById('compare-results');
    if (!resultsArea) return;

    resultsArea.classList.remove('hidden');
    this.generateComparisonTable();
  }

  hideResults() {
    const resultsArea = document.getElementById('compare-results');
    if (!resultsArea) return;

    resultsArea.classList.add('hidden');
  }

  generateComparisonTable() {
    const resultsContent = document.querySelector('.results-content');
    if (!resultsContent || !this.tanks[0] || !this.tanks[1]) return;

    const tank1 = this.tanks[0];
    const tank2 = this.tanks[1];

    resultsContent.innerHTML = `
      <div class="comparison-table">
        <div class="comparison-header">
          <div class="comparison-category">Характеристика</div>
          <div class="comparison-tank">
            <div class="comparison-tank-icon-wrapper">
              <img src="src/assets/images/tanks/${tank1.id}.webp" alt="${tank1.name}" class="comparison-tank-icon" onerror="this.style.display='none'">
            </div>
            <div class="comparison-tank-info">
              <div class="comparison-tank-header">
                <img src="${getFlagPath(tank1.country)}" alt="${tank1.country}" class="comparison-flag">
                <div class="comparison-tank-badge ${getTankTypeClass(tank1.type)}">${tank1.type}</div>
              </div>
              <span class="comparison-tank-name">${tank1.name}</span>
            </div>
          </div>
          <div class="comparison-tank">
            <div class="comparison-tank-icon-wrapper">
              <img src="src/assets/images/tanks/${tank2.id}.webp" alt="${tank2.name}" class="comparison-tank-icon" onerror="this.style.display='none'">
            </div>
            <div class="comparison-tank-info">
              <div class="comparison-tank-header">
                <img src="${getFlagPath(tank2.country)}" alt="${tank2.country}" class="comparison-flag">
                <div class="comparison-tank-badge ${getTankTypeClass(tank2.type)}">${tank2.type}</div>
              </div>
              <span class="comparison-tank-name">${tank2.name}</span>
            </div>
          </div>
        </div>

        ${this.generateComparisonRow('Урон', tank1, tank2, 'damage', '')}
        ${this.generateComparisonRow('Бронепробитие', tank1, tank2, 'penetration', ' мм')}
        ${this.generateComparisonRow('Прочность', tank1, tank2, 'hitPoints', ' HP')}
        ${this.generateComparisonRow('Максимальная скорость', tank1, tank2, 'speed', ' км/ч')}
        ${this.generateComparisonRow('Обзор', tank1, tank2, 'viewRange', ' м')}
        ${this.generateComparisonRow('Время перезарядки', tank1, tank2, 'reloadTime', ' сек', true)}
        ${this.generateComparisonRow('Время сведения', tank1, tank2, 'aimTime', ' сек', true)}
        ${this.generateComparisonRow('Разброс', tank1, tank2, 'dispersion', ' м', true)}
        ${this.generateComparisonRow('Мощность двигателя', tank1, tank2, 'enginePower', ' л.с.')}
        ${this.generateComparisonRow('Скорость поворота', tank1, tank2, 'traverse', ' °/сек')}
      </div>
    `;
  }

  generateComparisonRow(label, tank1, tank2, stat, unit = '', isLowerBetter = false) {
    const getStatValue = (tank, stat, defaultValue = 'Н/Д') => {
      if (
        tank.characteristics &&
        tank.characteristics[stat] !== undefined &&
        tank.characteristics[stat] !== null
      ) {
        return tank.characteristics[stat];
      }
      if (tank[stat] !== undefined && tank[stat] !== null) {
        return tank[stat];
      }
      return defaultValue;
    };

    const val1 = getStatValue(tank1, stat);
    const val2 = getStatValue(tank2, stat);

    let class1 = 'equal',
      class2 = 'equal';

    if (val1 !== 'Н/Д' && val2 !== 'Н/Д') {
      const num1 = parseFloat(val1);
      const num2 = parseFloat(val2);

      if (!isNaN(num1) && !isNaN(num2)) {
        if (isLowerBetter) {
          class1 = num1 < num2 ? 'better' : num1 > num2 ? 'worse' : 'equal';
          class2 = num2 < num1 ? 'better' : num2 > num1 ? 'worse' : 'equal';
        } else {
          class1 = num1 > num2 ? 'better' : num1 < num2 ? 'worse' : 'equal';
          class2 = num2 > num1 ? 'better' : num2 < num1 ? 'worse' : 'equal';
        }
      }
    }

    const displayVal1 = val1 === 'Н/Д' ? val1 : val1 + unit;
    const displayVal2 = val2 === 'Н/Д' ? val2 : val2 + unit;

    return `
      <div class="comparison-row">
        <div class="comparison-category">${label}</div>
        <div class="comparison-value ${class1}">${displayVal1}</div>
        <div class="comparison-value ${class2}">${displayVal2}</div>
      </div>
    `;
  }
}

// Создаем глобальный экземпляр
export const compareService = new CompareService();

// Экспортируе�� в глобальную область для доступа из HTML
if (typeof window !== 'undefined') {
  window.compareSystem = compareService;
}
