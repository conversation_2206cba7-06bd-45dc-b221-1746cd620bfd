// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';
import {
  getTankIconPath,
  getBlurredFlagPath,
  getTankTypeClass,
} from './constants.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  // This is now super-fast. We just remove the 'is-active' class from all sections.
  document.querySelectorAll('.content-section.is-active').forEach(section => {
    section.classList.remove('is-active');
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;

  const el = document.getElementById(id);
  if (el) {
    el.classList.add('is-active');
  }

  // If the 'vehicles' section is being shown, call the global render/filter function from main.js.
  // This ensures the tank list is always correctly displayed on navigation.
  if (menu === 'vehicles') {
    if (window.applyFiltersAndRenderTankList) {
      window.applyFiltersAndRenderTankList();
    } else {
      console.error('Render function not found on window object.');
    }
  }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Setup logo spin with random direction and speed
  const logo = sidebar.querySelector('.sidebar-logo-icon');
  if (logo) {
    let spinOutTimeout;
    // Removed unused variable: currentRotation

    // Функция для получения случайной анимации вращения
    const getRandomSpinAnimation = () => {
      const directions = ['spinClockwise', 'spinCounterClockwise'];

      // 30% шанс для очень быстрого вращения
      const isSuperFast = Math.random() < 0.3;

      let speeds;
      if (isSuperFast) {
        // Original super-fast speeds
        speeds = [
          { name: 'spinFast', duration: '0.4s' },
          { name: 'spinFast', duration: '0.5s' },
          { name: 'spinFast', duration: '0.6s' },
        ];
      } else {
        // Original normal speeds
        speeds = [
          { name: 'spinFast', duration: '0.8s' },
          { name: 'spin', duration: '1.2s' },
          { name: 'spinSlow', duration: '1.8s' },
        ];
      }

      const direction = directions[Math.floor(Math.random() * directions.length)];
      const speed = speeds[Math.floor(Math.random() * speeds.length)];

      return {
        animation: direction === 'spinClockwise' ? speed.name : 'spinCounterClockwise',
        duration: speed.duration,
        isSuperFast: isSuperFast,
      };
    };

    const smoothStop = () => {
      // Останавливаем текущую анимацию и получаем текущий угол поворота
      const computedStyle = window.getComputedStyle(logo);
      const transform = computedStyle.transform;

      // Плавная остановка с возвратом в исходное положение
      logo.style.animation = 'none';
      logo.style.transform = transform; // Фиксируем текущее положение

      // Запускаем плавную анимацию возврата к 0 градусов
      requestAnimationFrame(() => {
        logo.style.transition = 'transform 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
        logo.style.transform = 'rotate(0deg)';
        logo.style.animation = '';

        // Убираем transition после завершения анимации
        setTimeout(() => {
          logo.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          logo.classList.remove('super-fast-spin'); // Убираем класс супер-быстрого вращения
          // currentRotation = 0; // Removed unused variable
        }, 1200);
      });
    };

    logo.addEventListener('mouseenter', () => {
      clearTimeout(spinOutTimeout); // Stop any pending smoothStop

      // Immediately stop any ongoing CSS transitions and existing keyframe animations
      // to prevent them from interfering with the new animation.
      logo.style.transition = 'none';
      logo.style.animation = 'none'; // This clears all animation properties like name, duration, etc.

      // Apply the new animation in the next available frame.
      // This ensures that 'animation: none' and 'transition: none' take effect
      // before the new animation starts from the current visual state.
      requestAnimationFrame(() => {
        const randomSpin = getRandomSpinAnimation();

        // Set up the new animation properties individually for clarity and robustness
        logo.style.animationName = randomSpin.animation;
        logo.style.animationDuration = randomSpin.duration;
        logo.style.animationTimingFunction = 'linear';
        logo.style.animationIterationCount = 'infinite';
        logo.style.animationPlayState = 'running'; // Ensure it's running

        // Apply class for super-fast spin visual effects (e.g., different glow intensity)
        logo.classList.toggle('super-fast-spin', randomSpin.isSuperFast);
      });
    });

    logo.addEventListener('mouseleave', () => {
      spinOutTimeout = setTimeout(smoothStop, 2000);
    });
  }

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    console.log('[Sidebar] Click detected.');
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) {
      console.log('[Sidebar] Click was not on a menu item.');
      return;
    }
    e.preventDefault();

    const menuKey = item.dataset.section;
    console.log(`[Sidebar] Clicked on menu item with key: ${menuKey}`);
    if (menuKey) {
      onMenuSelected(menuKey);
    }
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

export function onMenuSelected(menu, initial = false) {
  console.log(`[onMenuSelected] Called with menu: ${menu}`);
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;

  // With the new class-based system, there's no more lag.
  // We can do everything synchronously without hacks.
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Handle the flag section animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    if (menu === 'vehicles') {
      if (initial) {
        // On initial load, a small delay helps the animation run smoothly.
        setTimeout(() => {
          flagSection.classList.add('open');
        }, 50);
      } else {
        flagSection.classList.add('open');
      }
    } else {
      flagSection.classList.remove('open');
    }
  }
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}

/**
 * Renders the list of tanks into the specified container.
 * @param {Array<Object>} tanksToRender - Array of tank objects to render.
 * @param {HTMLElement} tankListContainer - The container element to render tanks into.
 */
export function renderTankList(tanks) {
  const tankListContainer = document.getElementById('tank-list');
  if (!tankListContainer) {
    console.error('Tank list container not found');
    return;
  }

  // Clear previous content and prepare a document fragment for performance
  tankListContainer.innerHTML = '';
  const fragment = document.createDocumentFragment();

  if (!tanks || tanks.length === 0) {
    tankListContainer.innerHTML = '<p class="text-center text-gray-400 col-span-full">No tanks match the selected filters.</p>';
    return;
  }

  tanks.forEach(tank => {
    const tankTypeClass = getTankTypeClass(tank.type);
    const tankIconPath = getTankIconPath(tank.name);
    const blurredFlagPath = getBlurredFlagPath(tank.country);

    const tankItem = document.createElement('div');
    tankItem.className = `tank-item ${tankTypeClass}`;
    tankItem.dataset.tankId = tank.id || tank.name; // Use name as fallback ID
    tankItem.dataset.country = tank.country;
    tankItem.dataset.type = tank.type;

    // This structure matches the one expected by `tank-list.css`
    tankItem.innerHTML = `
      <div class="tank-flag-background" style="background-image: url('${blurredFlagPath}');"></div>
      <div class="tank-info-container">
        <div class="tank-type-container">
          <div class="tank-type-indicator ${tankTypeClass}">${tank.type}</div>
        </div>
        <div class="tank-name-row">
          <div class="tank-name">${tank.name}</div>
        </div>
      </div>
      <div class="tank-icon" style="background-image: url('${tankIconPath}');"></div>
    `;

    fragment.appendChild(tankItem);
  });

  tankListContainer.appendChild(fragment);
}

/**
 * Finds a tank by its ID and displays its details.
 * @param {string} tankId - The ID of the tank to display.
 * @param {object} allTanksData - The complete tanks data object.
 */
export function showTankDetails(tankId, allTanksData) {
  let foundTank = null;

  // Search for the tank in the nested data structure
  for (const country in allTanksData) {
    for (const type in allTanksData[country]) {
      const tank = allTanksData[country][type].find(t => (t.id || t.name) === tankId);
      if (tank) {
        foundTank = tank;
        break;
      }
    }
    if (foundTank) break;
  }

  if (!foundTank) {
    console.error(`Tank with ID ${tankId} not found.`);
    return;
  }

  // Populate the details container
  const detailsContainer = document.getElementById('tank-characteristics-container');
  const detailsContent = detailsContainer.querySelector('.tank-details-content');

  if (!detailsContainer || !detailsContent) {
    console.error('Tank details containers not found.');
    return;
  }

  // Example: Just showing the tank name for now. 
  // This should be expanded to show all characteristics.
  detailsContent.innerHTML = `
    <h2 class="text-2xl font-bold mb-4">${foundTank.name}</h2>
    <p>Country: ${foundTank.country}</p>
    <p>Type: ${foundTank.type}</p>
    <p>Tier: ${foundTank.tier}</p>
    <p>HP: ${foundTank.hp}</p>
    <p>Weight: ${foundTank.weight}t</p>
    // ... add more details as needed
  `;

  // Ensure the parent 'Vehicles' section is active, then hide the list and show the details.
  showSection('tank-list');

  const tankListContainer = document.getElementById('tank-list-container');
  if (tankListContainer) {
    tankListContainer.style.display = 'none';
  }

  // 'detailsContainer' is already defined in this scope, so we just use it.
  detailsContainer.style.display = 'block';
  detailsContainer.classList.remove('hidden');

  // Ensure the 'Vehicles' menu item remains active.
  updateActiveClass('vehicles');
}
