/* CSS Custom Properties (Variables) */
:root {
  /* Colors - Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --accent-primary: #3b82f6;
  --accent-secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme */
[data-theme='dark'] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
}

/* Sidebar variables - централизованные */
:root {
  --sidebar-width: 250px;
  --sidebar-bg: #1d1d2e;
  --sidebar-text: #e6e6e6;
  --sidebar-item-hover: rgba(154, 98, 230, 0.08);
  --sidebar-item-active: #7254e4;
  --sidebar-border: 1px solid rgba(61, 61, 90, 0.2);
  --sidebar-icon-color: #a17bf8;
  --sidebar-transition: all 0.2s ease;
  --unity-purple: #7254e4;
  --unity-purple-light: #a17bf8;
  --unity-dark: #27233d;
  --unity-darker: #1d1a30;
  --glass-highlight: rgba(255, 255, 255, 0.07);
  --glass-shadow: rgba(0, 0, 0, 0.1);
}
