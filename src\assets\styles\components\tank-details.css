/* Tank Details Styles - OPTIMIZED (Only unique styles) */

/* Container positioning and layout - FULL WIDTH WITHOUT RESTRICTIONS */
#tank-characteristics-container {
  width: 100%;
  max-width: none; /* Убираем ограничение ширины - на всю ширину */
  margin: -1rem; /* Компенсируем отступы родительского контейнера */
  padding: 0; /* Убираем внутренние отступы */
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;
  backdrop-filter: none;
  overflow: visible;
  box-sizing: border-box;
  position: static; /* Убираем любое позиционирование - обычный поток */
  z-index: auto;
  opacity: 0;
  transition: opacity 0.3s ease;
}

#tank-characteristics-container:not(.hidden) {
  opacity: 1;
}

/* Медиа-запросы для компенсации отступов */
@media (min-width: 768px) {
  #tank-characteristics-container {
    margin: -1.5rem; /* Компенсируем отступы 1.5rem на больших экранах */
  }
}

@media (max-width: 768px) {
  #tank-characteristics-container {
    margin: -0.5rem; /* Компенсируем отступы 0.5rem на мобильных */
  }
}

/* Unique styles only - duplicates removed and moved to tank-characteristics.css */

/* Equipment Section Integration */
.equipment-section {
  margin-top: 24px;
}

/* Responsive - Mobile only (desktop styles moved to tank-characteristics.css) */
@media (max-width: 768px) {
  #tank-characteristics-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0.5rem;
    overflow: visible;
    position: static; /* Убеждаемся что нет фиксированного позиционирования */
  }
}
