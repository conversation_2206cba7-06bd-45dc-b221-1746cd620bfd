// src/components/compare/CompareSystem.js
import { state } from '../../store/state.js';
import { createTankId } from '../../utils/tankUtils.js';
import { getTankIconPath, getRoleIconPath, getTankTypeClass, getFlagPath } from '../../utils/constants.js';
import { getCachedElement, performanceMonitor } from '../../utils/performance.js';

const compareSystem = {
  tanks: [null, null],
  activeSlot: null,

  addTank(tank, slotIndex) {
    if (slotIndex < 0 || slotIndex > 1) {
      console.error('[Compare] Invalid slot index:', slotIndex);
      return;
    }
    this.tanks[slotIndex] = { ...tank, id: createTankId(tank.name) };
    this.updateSlotDisplay(slotIndex);
    this.updateCompareButton();
    this.closeTankSelectorModal();
    console.log(`[Compare] Tank ${tank.name} added to slot ${slotIndex + 1}`);
  },

  removeTank(slotIndex) {
    if (slotIndex < 0 || slotIndex > 1) {
      console.error('[Compare] Invalid slot index:', slotIndex);
      return;
    }
    this.tanks[slotIndex] = null;
    this.updateSlotDisplay(slotIndex);
    this.updateCompareButton();
    this.hideResults();
    console.log(`[Compare] Tank removed from slot ${slotIndex + 1}`);
  },

  clearAll() {
    this.tanks = [null, null];
    this.updateSlotDisplay(0);
    this.updateSlotDisplay(1);
    this.updateCompareButton();
    this.hideResults();
    console.log('[Compare] All tanks cleared from comparison');
  },

  updateSlotDisplay(slotIndex) {
    const slotElement = getCachedElement(`#compare-slot-${slotIndex + 1}`);
    const tank = this.tanks[slotIndex];

    if (slotElement) {
      const nameElement = slotElement.querySelector('.compare-tank-name');
      const iconElement = slotElement.querySelector('.compare-tank-icon');
      const removeButton = slotElement.querySelector('.remove-compare-tank');
      const addButton = slotElement.querySelector('.add-compare-tank');

      if (tank) {
        if (nameElement) nameElement.textContent = tank.name;
        if (iconElement) {
          iconElement.src = getTankIconPath(tank.name);
          iconElement.alt = tank.name;
          iconElement.classList.remove('hidden');
        }
        if (removeButton) removeButton.classList.remove('hidden');
        if (addButton) addButton.classList.add('hidden');
        slotElement.classList.add('has-tank');
      } else {
        if (nameElement) nameElement.textContent = `Танк ${slotIndex + 1}`;
        if (iconElement) {
          iconElement.src = '/assets/images/tank_placeholder.svg';
          iconElement.alt = 'Выберите танк';
        }
        if (removeButton) removeButton.classList.add('hidden');
        if (addButton) addButton.classList.remove('hidden');
        slotElement.classList.remove('has-tank');
      }
    } else {
      console.warn(`[Compare] Slot element #compare-slot-${slotIndex + 1} not found`);
    }
  },

  updateCompareButton() {
    const compareButton = getCachedElement('#compare-action-button');
    if (compareButton) {
      const canCompare = this.tanks[0] && this.tanks[1];
      compareButton.disabled = !canCompare;
      compareButton.classList.toggle('opacity-50', !canCompare);
      compareButton.classList.toggle('cursor-not-allowed', !canCompare);
    }
  },

  showResults() {
    if (!this.tanks[0] || !this.tanks[1]) {
      console.warn('[Compare] Cannot show results, not enough tanks selected.');
      return;
    }
    const resultsContainer = getCachedElement('#compare-results-container');
    const tableContainer = getCachedElement('#compare-table-container');
    if (resultsContainer && tableContainer) {
      tableContainer.innerHTML = this.generateComparisonTable(); // Simplified HTML
      resultsContainer.classList.remove('hidden');
      resultsContainer.scrollIntoView({ behavior: 'smooth' });
      console.log('[Compare] Showing comparison results.');
    }
  },

  hideResults() {
    const resultsContainer = getCachedElement('#compare-results-container');
    if (resultsContainer) {
      resultsContainer.classList.add('hidden');
      console.log('[Compare] Hiding comparison results.');
    }
  },

  generateComparisonTable() {
    const [tank1, tank2] = this.tanks;
    if (!tank1 || !tank2) return '<p>Ошибка: Не выбраны оба танка для сравнения.</p>';

    // Убедимся, что у танков есть ID для иконок, если createTankId не был вызван ранее на них
    const t1Id = tank1.id || createTankId(tank1.name);
    const t2Id = tank2.id || createTankId(tank2.name);

    let tableHTML = `<div class="comparison-header grid grid-cols-3 items-center gap-2 p-2 md:p-4 bg-gray-800 dark:bg-gray-750 rounded-t-lg">
                        <div class="comparison-tank-info flex flex-col items-center text-center">
                            <img src="${getTankIconPath(tank1.name, t1Id)}" alt="${tank1.name}" class="comparison-tank-image w-20 h-16 md:w-32 md:h-24 object-cover mb-2 rounded border-2 border-gray-600 dark:border-gray-500">
                            <h3 class="text-xs md:text-sm font-semibold text-blue-400 dark:text-blue-300">${tank1.name}</h3>
                            <p class="text-xxs md:text-xs text-gray-400 dark:text-gray-300">${tank1.country} - ${tank1.type}</p>
                        </div>
                        <div class="comparison-vs text-center text-lg md:text-2xl font-bold text-gray-400 dark:text-gray-300">VS</div>
                        <div class="comparison-tank-info flex flex-col items-center text-center">
                            <img src="${getTankIconPath(tank2.name, t2Id)}" alt="${tank2.name}" class="comparison-tank-image w-20 h-16 md:w-32 md:h-24 object-cover mb-2 rounded border-2 border-gray-600 dark:border-gray-500">
                            <h3 class="text-xs md:text-sm font-semibold text-blue-400 dark:text-blue-300">${tank2.name}</h3>
                            <p class="text-xxs md:text-xs text-gray-400 dark:text-gray-300">${tank2.country} - ${tank2.type}</p>
                        </div>
                    </div>
                    <table class="comparison-table w-full text-sm text-left text-gray-400 dark:text-gray-300 bg-gray-850 dark:bg-gray-780 rounded-b-lg">`;

    const characteristics = [
      { label: 'Прочность', stat: 'hp', unit: 'HP' },
      { label: 'Мощность двигателя', stat: 'engine_power', unit: 'л.с.' },
      { label: 'Масса', stat: 'weight', unit: 'т' },
      { label: 'Удельная мощность', stat: 'power_to_weight_ratio', unit: 'л.с./т' },
      { label: 'Макс. скорость вперед', stat: 'speed_forward', unit: 'км/ч' },
      { label: 'Макс. скорость назад', stat: 'speed_backward', unit: 'км/ч' },
      { label: 'Бронирование корпуса (лоб/борт/корма)', stat: 'hull_armor' },
      { label: 'Бронирование башни (лоб/борт/корма)', stat: 'turret_armor' },
      { label: 'Обзор', stat: 'view_range', unit: 'м' },
      { label: 'Дальность связи', stat: 'signal_range', unit: 'м' },
      { label: 'Урон', stat: 'gun_damage', unit: 'ед.' },
      { label: 'Бронепробиваемость', stat: 'gun_penetration', unit: 'мм' },
      { label: 'Скорострельность', stat: 'gun_rate_of_fire', unit: 'выстр./мин' },
      { label: 'Время сведения', stat: 'gun_aim_time', unit: 'с', isLowerBetter: true },
      { label: 'Разброс на 100 м', stat: 'gun_dispersion', unit: 'м', isLowerBetter: true },
      { label: 'Углы ВН', stat: 'gun_elevation_depression' },
      { label: 'Углы ГН', stat: 'gun_traverse_arc' },
    ];

    characteristics.forEach(char => {
      // Предполагаем, что generateComparisonRow существует в this (т.е. в compareSystem)
      tableHTML += this.generateComparisonRow(char.label, tank1, tank2, char.stat, char.unit, char.isLowerBetter);
    });

    tableHTML += '</table>';
    return tableHTML;
  },

  generateComparisonRow(label, tank1, tank2, stat, unit = '', isLowerBetter = false) {
    const val1 = tank1.characteristics[stat] || 'N/A';
    const val2 = tank2.characteristics[stat] || 'N/A';

    let class1 = '';
    let class2 = '';

    // Пытаемся извлечь числовые значения, даже если они содержат нечисловые символы (например, '123 мм')
    // или являются диапазонами (например, '100-200'). Для диапазонов возьмем первое число.
    const parseValue = (v) => {
      if (typeof v === 'number') return v;
      if (typeof v === 'string') {
        // Удаляем все, кроме цифр, точки, минуса. Для диапазонов берем первое число.
        const cleanedString = String(v).split('-')[0].replace(/[^0-9.-]+/g, "");
        return parseFloat(cleanedString);
      }
      return NaN;
    };

    const numVal1 = parseValue(val1);
    const numVal2 = parseValue(val2);

    if (!isNaN(numVal1) && !isNaN(numVal2)) {
      if (isLowerBetter) {
        if (numVal1 < numVal2) { class1 = 'text-green-400 dark:text-green-300 font-semibold'; class2 = 'text-red-400 dark:text-red-300'; }
        if (numVal2 < numVal1) { class2 = 'text-green-400 dark:text-green-300 font-semibold'; class1 = 'text-red-400 dark:text-red-300'; }
      } else {
        if (numVal1 > numVal2) { class1 = 'text-green-400 dark:text-green-300 font-semibold'; class2 = 'text-red-400 dark:text-red-300'; }
        if (numVal2 > numVal1) { class2 = 'text-green-400 dark:text-green-300 font-semibold'; class1 = 'text-red-400 dark:text-red-300'; }
      }
    }
    // Форматируем отображаемое значение, добавляя единицу измерения, если она есть
    const displayVal1 = val1 + (unit ? ' ' + unit : '');
    const displayVal2 = val2 + (unit ? ' ' + unit : '');

    return `
      <tr class="border-b border-gray-700 dark:border-gray-600 hover:bg-gray-700 dark:hover:bg-gray-650 transition-colors duration-100">
        <td class="py-2.5 px-3 md:px-4 font-medium text-gray-300 dark:text-gray-200 whitespace-nowrap">${label}</td>
        <td class="py-2.5 px-3 md:px-4 text-center ${class1}">${displayVal1}</td>
        <td class="py-2.5 px-3 md:px-4 text-center ${class2}">${displayVal2}</td>
      </tr>
    `;
  },

  _getTanksWithIds() {
    if (!state.allTanks || state.allTanks.length === 0) {
      console.error("[CompareSelector] allTanks is not initialized!");
      return [];
    }
    return state.allTanks.map(tank => ({
      ...tank,
      id: createTankId(tank.name)
    }));
  },

  _createTankSelectorHTML(tank) {
    const tankId = createTankId(tank.name);
    const iconPath = getTankIconPath(tank.name);
    const typeClass = getTankTypeClass(tank.type) || 'default-type';
    const flagPath = getFlagPath(tank.country);
    // Предполагаем, что tank.role это строка, если нет, нужна адаптация getRoleIconPath или данных
    const roleIconPath = getRoleIconPath(tank.role || tank.type); 

    return `
      <div class="tank-selector-item flex items-center p-3 hover:bg-gray-700 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-600 dark:border-gray-500 transition-colors duration-150 ease-in-out" data-tank-id="${tankId}" data-tank-name="${tank.name}">
        <img src="${iconPath}" alt="${tank.name}" class="w-16 h-10 md:w-20 md:h-12 object-cover mr-3 rounded border border-gray-500 dark:border-gray-400">
        <div class="flex-grow">
          <h4 class="text-sm md:text-base font-semibold text-blue-400 dark:text-blue-300 group-hover:text-white">${tank.name}</h4>
          <div class="flex items-center text-xs md:text-sm text-gray-400 dark:text-gray-300 mt-1">
            <img src="${flagPath}" alt="${tank.country}" class="w-4 h-3 mr-1.5">
            <span>${tank.country}</span>
            <span class="mx-1.5">|</span>
            <img src="${roleIconPath}" alt="${tank.role || tank.type}" class="w-3.5 h-3.5 mr-1.5 filter-contrast dark:filter-none" title="${tank.role || tank.type}">
            <span class="${typeClass}">${tank.type}</span>
          </div>
        </div>
        <button class="add-to-compare-btn ml-auto p-2 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
          Добавить
        </button>
      </div>
    `;
  },

  _addTankSelectorEventListeners(modalElement, tanks, slotIndex) {
    const tankItems = modalElement.querySelectorAll('.tank-selector-item');
    tankItems.forEach(item => {
      item.addEventListener('click', () => {
        const tankName = item.dataset.tankName;
        const selectedTank = tanks.find(t => t.name === tankName);
        if (selectedTank) {
          this.addTank(selectedTank, slotIndex);
        } else {
          console.error(`[CompareSelector] Tank with name ${tankName} not found in provided list.`);
        }
      });
    });
  },
  
  _populateTankSelector(modalElement, slotIndex) {
    const tankListContainer = modalElement.querySelector('.tank-selector-list');
    if (!tankListContainer) {
        console.error("[CompareSelector] Tank list container not found in modal.");
        return;
    }
    
    const tanks = this._getTanksWithIds();
    if (!tanks || tanks.length === 0) {
        tankListContainer.innerHTML = '<p>Нет танков.</p>'; // Simplified
        return;
    }

    tankListContainer.innerHTML = tanks.map(tank => this._createTankSelectorHTML(tank)).join('');
    this._addTankSelectorEventListeners(modalElement, tanks, slotIndex);
  },

  _filterTankSelector(modalElement, searchTerm, slotIndex) {
    const tankListContainer = modalElement.querySelector('.tank-selector-list');
     if (!tankListContainer) {
        console.error("[CompareSelector] Tank list container not found in modal for filtering.");
        return;
    }
    const tanks = this._getTanksWithIds();
    const lowerSearchTerm = searchTerm.toLowerCase();
    const filteredTanks = tanks.filter(tank => tank.name.toLowerCase().includes(lowerSearchTerm));

    if (filteredTanks.length > 0) {
      tankListContainer.innerHTML = filteredTanks.map(tank => this._createTankSelectorHTML(tank)).join('');
      this._addTankSelectorEventListeners(modalElement, filteredTanks, slotIndex);
    } else {
      // УПРОЩЕННЫЙ HTML
      tankListContainer.innerHTML = `<p>Танки \"${searchTerm}\" не найдены (упрощенно).</p>`;
    }
  },

  openTankSelector(slotIndex) {
    this.activeSlot = slotIndex;
    const modalElement = getCachedElement('#tank-selector-modal');
    if (!modalElement) {
      console.error('[Compare] Tank selector modal not found!');
      return;
    }

    const modalTitle = modalElement.querySelector('#tank-selector-title');
    if (modalTitle) modalTitle.textContent = `Выберите танк для слота ${slotIndex + 1}`;
    
    this._populateTankSelector(modalElement, slotIndex);

    const searchInput = modalElement.querySelector('#tank-selector-search');
    if (searchInput) {
      searchInput.value = '';
      const debouncedFilter = getCachedElement('tankSelectorSearchDebouncer') || performanceMonitor.debounce((event) => {
         this._filterTankSelector(modalElement, event.target.value, this.activeSlot);
      }, 300);
      if (!getCachedElement('tankSelectorSearchDebouncer')) {
        state.uiCache.set('tankSelectorSearchDebouncer', debouncedFilter);
      }
      searchInput.oninput = debouncedFilter;
    }

    modalElement.classList.remove('hidden');
    console.log(`[Compare] Opened tank selector for slot ${slotIndex}`);
  },

  closeTankSelectorModal() {
    const modalElement = getCachedElement('#tank-selector-modal');
    if (modalElement) {
      modalElement.classList.add('hidden');
      this.activeSlot = null;
      console.log('[Compare] Closed tank selector modal.');
    }
  }
};

export function initializeCompareSystem() {
  const addTankSlot1 = getCachedElement('#add-tank-slot-1');
  const addTankSlot2 = getCachedElement('#add-tank-slot-2');
  const removeTankSlot1 = getCachedElement('#remove-tank-slot-1');
  const removeTankSlot2 = getCachedElement('#remove-tank-slot-2');
  const compareButton = getCachedElement('#compare-action-button');
  const clearCompareButton = getCachedElement('#clear-compare-button');
  const closeModalButton = getCachedElement('#close-tank-selector-modal');

  if (addTankSlot1) addTankSlot1.addEventListener('click', () => compareSystem.openTankSelector(0));
  if (addTankSlot2) addTankSlot2.addEventListener('click', () => compareSystem.openTankSelector(1));
  
  if (removeTankSlot1) removeTankSlot1.addEventListener('click', () => compareSystem.removeTank(0));
  if (removeTankSlot2) removeTankSlot2.addEventListener('click', () => compareSystem.removeTank(1));
  
  if (compareButton) compareButton.addEventListener('click', () => compareSystem.showResults());
  if (clearCompareButton) clearCompareButton.addEventListener('click', () => compareSystem.clearAll());
  
  if (closeModalButton) closeModalButton.addEventListener('click', () => compareSystem.closeTankSelectorModal());

  compareSystem.updateSlotDisplay(0);
  compareSystem.updateSlotDisplay(1);
  compareSystem.updateCompareButton();

  console.log('[Compare] System initialized.');
}

export { compareSystem };
