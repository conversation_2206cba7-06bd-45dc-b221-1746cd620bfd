// src/store/state.js
// Centralized application state. Replace with reactive store later if needed.

export const state = {
  // navigation & UI
  currentMenuName: 'overview',
  restoringTankFromReload: false,

  // filters & selection
  selectedTank: null,
  selectedCountry: 'all',
  selectedCategory: 'all',
  searchQuery: '',

  // sorting state
  sortColumn: -1,
  sortDirection: 0,

  // loaded data
  allTanks: [],
  selectedEquipment: [null, null, null],
  selectedInstruction: null,

  // builds
  currentBuilds: {},
  defaultBuilds: {},
};

// Temporary: expose for legacy global access while refactor is ongoing
if (typeof window !== 'undefined') {
  window.state = state;
}
