/**
 * Throttles a function, ensuring it's called at most once per interval.
 */
export const throttle = (fn, ms) => {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      fn.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), ms);
    }
  };
};

/**
 * Debounces a function, delaying execution until after wait milliseconds.
 */
export const debounce = (fn, ms = 300) => {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), ms);
  };
};

/**
 * Slugifies a string: lowercases latin letters, replaces spaces and non-alphanumerics with hyphens
 */
export function slugify(name) {
  return name
    .toString()
    .normalize('NFD') // decompose accents
    .replace(/[^\w\s-а-яА-Я]/g, '') // remove non word/space/hyphen (including punctuation)
    .replace(/[\u0300-\u036f]/g, '') // remove diacritics
    .toLowerCase()
    .replace(/\s+/g, '-') // replace spaces with hyphens
    .replace(/-+/g, '-') // collapse multiple hyphens
    .replace(/^-|-$/g, ''); // trim leading/trailing hyphens
}

import { getTankIconPath } from './constants.js';

/**
 * Creates a tank ID from tank name using the constants module
 */
export function createTankId(tankName) {
  // Используем функцию из constants.js для получения имени файла иконки
  const iconPath = getTankIconPath(tankName);
  // Извлекаем имя файла без расширения и пути
  return iconPath.split('/').pop().replace('.webp', '');
}
