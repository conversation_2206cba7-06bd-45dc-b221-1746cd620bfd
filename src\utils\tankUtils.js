import { getTankIconPath } from './constants.js';

/**
 * Функция для создания ID танка на основе имени.
 * ID используется, например, в URL hash.
 * @param {string} tankName - Имя танка.
 * @returns {string} ID танка.
 */
export function createTankId(tankName) {
  // Используем функцию из constants.js для получения имени файла иконки
  const iconPath = getTankIconPath(tankName);
  // Извлекаем имя файла без расширения и пути
  return iconPath.split('/').pop().replace('.webp', '');
}
