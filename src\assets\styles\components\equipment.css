/* Equipment Slots Styles - БАЗОВЫЕ СТИЛИ (переопределяются в tank-characteristics.css) */
.equipment-slots-container {
  display: flex;
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.equipment-modal .equipment-slot {
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.equipment-modal .equipment-slot:hover {
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.equipment-modal .equipment-slot.has-equipment {
  background: rgba(0, 0, 0, 0.3);
  border-color: #4a90e2;
}

.selected-equipment-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 8px;
}

.remove-equipment-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 24px;
  height: 24px;
  background: rgba(255, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 18px;
  line-height: 1;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.equipment-modal .equipment-slot:hover .remove-equipment-btn {
  opacity: 1;
}

.remove-equipment-btn:hover {
  background: rgba(255, 0, 0, 1);
}

.equipment-slot-tooltip {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.equipment-modal .equipment-slot:hover .equipment-slot-tooltip {
  opacity: 1;
}

/* Equipment Modal */
.equipment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.equipment-modal.active {
  display: flex;
}

.equipment-modal-content {
  background: var(--bg-secondary);
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.equipment-modal-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.equipment-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.equipment-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.equipment-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.equipment-modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

/* Equipment List */
#equipment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.equipment-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 15px;
}

.equipment-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.equipment-icon {
  width: 60px;
  height: 60px;
  object-fit: contain;
  flex-shrink: 0;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.equipment-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.equipment-bonuses {
  font-size: 0.85rem;
}

.bonus-item {
  color: #4ade80;
  display: inline-block;
  margin-right: 10px;
}

.bonus-item:first-child {
  display: block;
}

/* Equipment Table Styles */
#equipment-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: hidden;
}

#equipment-table thead {
  background: rgba(255, 255, 255, 0.05);
}

#equipment-table th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  position: relative;
  cursor: pointer;
  user-select: none;
}

#equipment-table th:hover {
  background: rgba(255, 255, 255, 0.05);
}

#equipment-table th.sortable::after {
  content: '↕';
  position: absolute;
  right: 8px;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

#equipment-table th.sortable:hover::after {
  opacity: 0.6;
}

#equipment-table th.sorted-asc::after {
  content: '↑';
  opacity: 1;
}

#equipment-table th.sorted-desc::after {
  content: '↓';
  opacity: 1;
}

#equipment-table tbody tr {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s ease;
}

#equipment-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.03);
}

#equipment-table td {
  padding: 10px 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  position: relative;
}

.build-link {
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.build-link:hover {
  filter: brightness(1.3);
  text-decoration: underline;
}

/* Progress bars in table cells */
.table-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #4ade80 0%, #22c55e 100%);
  transition: width 0.6s ease;
  opacity: 0.7;
  border-radius: 0 3px 3px 0;
}

.table-progress-bar.low {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.table-progress-bar.medium {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.table-progress-bar.high {
  background: linear-gradient(90deg, #4ade80 0%, #22c55e 100%);
}

/* Tank error message */
#tank-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #f87171;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 20px 0;
  font-size: 0.9rem;
  display: none;
}

#tank-error.hidden {
  display: none;
}

#tank-error:not(.hidden) {
  display: block;
}

/* Dark theme adjustments */
[data-theme='dark'] .equipment-modal .equipment-slot {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .equipment-modal .equipment-slot:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme='dark'] .equipment-modal-content {
  background: #1a1a1a;
}

[data-theme='dark'] .equipment-item {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.05);
}

[data-theme='dark'] .equipment-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] #equipment-table {
  background: rgba(0, 0, 0, 0.2);
}

[data-theme='dark'] #equipment-table thead {
  background: rgba(255, 255, 255, 0.03);
}

[data-theme='dark'] #equipment-table th {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

[data-theme='dark'] #equipment-table tbody tr {
  border-bottom-color: rgba(255, 255, 255, 0.02);
}

[data-theme='dark'] #equipment-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* Responsive design */
@media (max-width: 768px) {
  /* Убрано - конфликтовало с позиционированием в tank-characteristics.css */

  .equipment-modal .equipment-slot {
    width: 70px;
    height: 70px;
  }

  #equipment-list {
    grid-template-columns: 1fr;
  }

  .equipment-modal-content {
    width: 95%;
    max-height: 90vh;
  }

  #equipment-table {
    font-size: 0.8rem;
  }

  #equipment-table th,
  #equipment-table td {
    padding: 8px 4px;
  }

  .table-progress-bar {
    height: 2px;
  }
}
