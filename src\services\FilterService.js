// src/filters.js
// Centralised filter utilities used across the application

// Map Russian abbreviations to internal tank type identifiers used in data structures
export const RUSSIAN_TO_INTERNAL_TYPE_MAP = {
  ЛТ: 'lightTank',
  СТ: 'mediumTank',
  ТТ: 'heavyTank',
  'ПТ-САУ': 'at-spg',
  САУ: 'spg',
};

// Inverse mapping for UI display purposes
export const INTERNAL_TO_RUSSIAN_TYPE_MAP = Object.fromEntries(
  Object.entries(RUSSIAN_TO_INTERNAL_TYPE_MAP).map(([rus, internal]) => [internal, rus])
);

// Display order for Russian tank type abbreviations
export const TANK_TYPE_ORDER = {
  ЛТ: 1,
  СТ: 2,
  ТТ: 3,
  'ПТ-САУ': 4,
  САУ: 5,
};

// Legacy English mapping for backward compatibility
export const ENGLISH_TO_INTERNAL_TYPE_MAP = {
  LT: 'lightTank',
  MT: 'mediumTank',
  HT: 'heavyTank',
  TD: 'at-spg',
  SPG: 'spg',
};

export const INTERNAL_TO_ENGLISH_TYPE_MAP = Object.fromEntries(
  Object.entries(ENGLISH_TO_INTERNAL_TYPE_MAP).map(([eng, internal]) => [internal, eng])
);

// Map alternative / UI country identifiers to canonical keys used in data.js
export const COUNTRY_ALIASES = {
  // Accept various spellings but canonical key is "international"
  international: 'international',
  intunion: 'international', // legacy spelling in older data
  "int'l union": 'international',
};

function normalizeCountry(value) {
  if (!value) return value;
  const lower = value.toLowerCase();
  return COUNTRY_ALIASES[lower] || lower;
}

/**
 * Check if a tank satisfies the active country and category filters.
 *
 * @param {Object} tank                Tank object from state.allTanks
 * @param {Object} filters             { country: string, type: string }
 * @returns {boolean}                  True if the tank matches all filters
 */
export function matchesFilters(tank, filters) {
  const { country: selectedCountries, type: selectedTypes } = filters;

  // Country check: if country filters are active, the tank's country must be in the list.
  if (selectedCountries && Array.isArray(selectedCountries) && selectedCountries.length > 0) {
    const tankCountry = normalizeCountry(tank.country);
    const normalizedSelectedCountries = selectedCountries.map(normalizeCountry);
    if (!normalizedSelectedCountries.includes(tankCountry)) {
      return false;
    }
  }

  // Type check: if type filters are active, the tank's type must be in the list.
  if (selectedTypes && Array.isArray(selectedTypes) && selectedTypes.length > 0) {
    // The `tank.type` is the display value (e.g., 'ЛТ'), which matches the filter values.
    if (!selectedTypes.includes(tank.type)) {
      return false;
    }
  }

  return true;
}

/**
 * Produce a sorted list of tanks that satisfy the current filters and search query.
 *
 * @param {Object} state         An object containing allTanks, selectedCountry (array), selectedCategory (array), and searchQuery.
 * @param {TankSearchIndex} searchIndex  Trie-based search index instance (optional).
 * @returns {Array<Object>}      Array of tank objects ready for rendering.
 */
export function getFilteredTanks(state, searchIndex) {
  const { selectedCountry, selectedCategory, searchQuery, allTanks } = state;

  // Flatten the nested tanksData object into a single array,
  // injecting 'country' and 'type' properties into each tank object.
  const flatTanks = [];
  if (allTanks && typeof allTanks === 'object') {
    for (const country in allTanks) {
      if (Object.prototype.hasOwnProperty.call(allTanks, country)) {
        for (const type in allTanks[country]) {
          if (Object.prototype.hasOwnProperty.call(allTanks[country], type)) {
            const tanks = allTanks[country][type] || [];
            tanks.forEach(tank => {
              flatTanks.push({
                ...tank,
                country: country,
                type: type,
              });
            });
          }
        }
      }
    }
  }

  let filtered;

  if (searchQuery) {
    const lowerCaseQuery = searchQuery.toLowerCase();
    const searchResults = flatTanks.filter(tank => tank.name.toLowerCase().includes(lowerCaseQuery));
    
    filtered = searchResults.filter(tank =>
      matchesFilters(tank, { country: selectedCountry, type: selectedCategory })
    );
  } else {
    // No search term – simply filter the flattened list of tanks
    filtered = flatTanks.filter(tank =>
      matchesFilters(tank, { country: selectedCountry, type: selectedCategory })
    );
  }

  // Sort primarily by type (Russian abbreviation order) and secondarily by name
  filtered.sort((a, b) => {
    const orderA = TANK_TYPE_ORDER[a.type] ?? 999;
    const orderB = TANK_TYPE_ORDER[b.type] ?? 999;

    if (orderA !== orderB) return orderA - orderB;
    return a.name.localeCompare(b.name);
  });

  return filtered;
}
